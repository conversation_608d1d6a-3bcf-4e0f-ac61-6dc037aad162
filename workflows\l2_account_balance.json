{"id": "l2_account_balance", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"account_id": "account_id", "latencyPreprocessing": "latencyPreprocessing", "clean_text": "clean_text", "fallback_message": "fallback_message"}}, {"step": "filler_tts", "process": "filler_tts_process", "input": {"fallback_message": "fallback_message"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyFillerTTS": "latencyFillerTTS"}}, {"step": "processing", "process": "processing_process", "input": {"account_id": "account_id"}, "tools": {"external_tools": "banking_api"}, "output": {"account_balance": "account_balance", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "input": {"text": "account_balance"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 3, "fallback_state": "l2_fallback_balance"}, "outputs": ["account_balance", "confirmation_message"]}