{"workflow": {"id": "banking_customer_service", "name": "Banking Customer Service", "version": "1.0", "start": "Greeting", "allowed_actions": ["Check account balance", "Transfer funds", "Apply for loan", "Report lost card", "Update personal information"], "prohibited_actions": ["Do not share PINs or passwords", "Do not process transactions without verification", "Do not disclose sensitive account details"], "states": {"Greeting": {"id": "Greeting", "type": "input", "layer2_id": "l2_greeting_banking_system", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'account_balance'", "target": "CheckBalance"}, {"condition": "intent == 'fund_transfer'", "target": "TransferFunds"}, {"condition": "intent == 'loan_application'", "target": "LoanApplication"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"]}, "CheckBalance": {"id": "CheckBalance", "type": "inform", "layer2_id": "l2_account_balance", "expected_input": ["account_id"], "expected_output": ["account_balance"], "transitions": [{"condition": "true", "target": "OfferMoreHelp"}], "allowed_tools": ["LLM", "TTS", "DB_QUERY"]}, "TransferFunds": {"id": "TransferFunds", "type": "transaction", "layer2_id": "l2_fund_transfer", "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["confirmation_number"], "transitions": [{"condition": "true", "target": "OfferMoreHelp"}], "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"]}, "LoanApplication": {"id": "LoanApplication", "type": "process", "layer2_id": "l2_loan_application", "expected_input": ["income", "credit_score", "requested_amount"], "expected_output": ["loan_approval_status", "interest_rate"], "transitions": [{"condition": "loan_approval_status == 'approved'", "target": "LoanApproved"}, {"condition": "loan_approval_status == 'denied'", "target": "LoanDenied"}], "allowed_tools": ["LLM", "TTS", "CREDIT_CHECK"]}, "LoanApproved": {"id": "LoanApproved", "type": "inform", "layer2_id": "l2_loan_approved", "expected_input": [], "expected_output": ["next_steps"], "transitions": [{"condition": "true", "target": "OfferMoreHelp"}], "allowed_tools": ["LLM", "TTS"]}, "LoanDenied": {"id": "LoanDenied", "type": "inform", "layer2_id": "l2_loan_denied", "expected_input": [], "expected_output": ["denial_reason"], "transitions": [{"condition": "true", "target": "OfferMoreHelp"}], "allowed_tools": ["LLM", "TTS"]}, "OfferMoreHelp": {"id": "OfferMoreHelp", "type": "decision", "layer2_id": "l2_more_help", "expected_input": [], "expected_output": ["user_response"], "transitions": [{"condition": "user_response == 'no'", "target": "Goodbye"}], "allowed_tools": ["STT", "LLM", "TTS"]}, "Goodbye": {"id": "Goodbye", "type": "end", "layer2_id": "l2_goodbye_banking_system", "expected_input": [], "expected_output": ["exit_signal"], "transitions": [], "allowed_tools": ["TTS"]}}}}