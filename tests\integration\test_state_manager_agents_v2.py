import sys
import asyncio
import os
import time
from pathlib import Path
from dotenv import load_dotenv
# Add project root to path for imports
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

load_dotenv()

from core.state_manager.state_manager import StateManager
from core.logging.logger_config import setup_development_logging, get_module_logger, cleanup_logger
from core.memory.redis_context import RedisClient
from schemas.outputSchema import StateOutput, StatusType, StatusCode
from schemas.a2a_message import A2AMessage, MessageType

logger = get_module_logger("test_state_manager_agents_v2", session_id="test_state_manager_agents_v2")

async def run_trial(sessionId: str = "test_session", userId: str = "user_1"):
    try:
        sm = await StateManager.create("banking_workflow.json", sessionId, userId)
        
        # test 1 - test getting a workflow
        workflow = await sm.getWorkflow()
        print("Workflow:", workflow)

        # test 2 - test getting all pipelines
        pipelines = await sm.getFullPipelineMap()
        print("Pipelines:", pipelines)

        # test 3 - test getting the current workflow state
        current_state = await sm.getCurrentWorkflowState()
        print("Current State:", current_state)

        # test 4 - test getting the current pipeline state
        current_pipeline_state = await sm.getCurrentPipelineState()
        print("Current Pipeline State:", current_pipeline_state)

        # test 5 - test executing a pipeline
        pipeline = await sm.getCurrentPipeline()
        print("Current Pipeline:", pipeline)

        # test 6 - test getting prohibited actions
        prohibited_actions = await sm.getProhibitedActions()
        print("Prohibited Actions:", prohibited_actions)

        # test 7 - test getting allowed actions
        allowed_actions = await sm.getAllowedActions()
        print("Allowed Actions:", allowed_actions)

        # test 8 - execution 

        # Step 1: Simulate greeting state
        
        # start by stt_process
        greeting_input = {"audio_path": os.path.join("fillerWords", "لحضة واحدة مع حضرتك.mp3")}
        greeting_stt_Result = await sm.executePipelineState(greeting_input)
        print("greeting_stt_Result:", greeting_stt_Result)

        # go to the preprocessing state
        await sm.transitionPipeline("preprocessing")
        greeting_preprocessing_input = {"transcript":greeting_stt_Result.outputs["text"]}
        greeting_preprocessing_result = await sm.executePipelineState(greeting_preprocessing_input)
        print("greeting_preprocessing_result:", greeting_preprocessing_result)

        # go to the filler tts state
        await sm.transitionPipeline("filler_tts")
        greeting_filler_tts_input = {"filler_text":"I am here with you, just a second"}
        greeting_filler_tts_result = await sm.executePipelineState(greeting_filler_tts_input)
        print("greeting_filler_tts_result:", greeting_filler_tts_result)

        # go to the processing state
        await sm.transitionPipeline("processing")
        greeting_processing_input = {"clean_text":greeting_preprocessing_result.outputs["clean_text"], "intent":greeting_preprocessing_result.outputs["intent"], "emotion":greeting_preprocessing_result.outputs["emotion"], "gender":greeting_preprocessing_result.outputs["gender"]}
        greeting_processing_result = await sm.executePipelineState(greeting_processing_input)
        print("greeting_processing_result:", greeting_processing_result)

        # go to the tts state
        await sm.transitionPipeline("tts")
        greeting_tts_input = {"text":greeting_processing_result.outputs["llm_answer"], "emotion":greeting_preprocessing_result.outputs["emotion"], "gender":greeting_preprocessing_result.outputs["gender"]}
        greeting_tts_result = await sm.executePipelineState(greeting_tts_input)
        print("greeting_tts_result:", greeting_tts_result)

        # Step 2: Simulate state 2
        await sm.transitionWorkflow("state_2")
        state_2_stt_input = {"audio_path": "fillerWords\لحضة واحدة مع حضرتك.mp3"}
        state_2_stt_result = await sm.executePipelineState(state_2_stt_input)
        print("state_2_stt_result:", state_2_stt_result)

        # go to the preprocessing state
        await sm.transitionPipeline("preprocessing")
        state_2_preprocessing_input = {"transcript":state_2_stt_result.outputs["text"]}
        state_2_preprocessing_result = await sm.executePipelineState(state_2_preprocessing_input)
        print("state_2_preprocessing_result:", state_2_preprocessing_result)

        # go to the processing state
        await sm.transitionPipeline("processing")
        state_2_processing_input = {"clean_text":state_2_preprocessing_result.outputs["clean_text"], "intent":state_2_preprocessing_result.outputs["intent"], "emotion":state_2_preprocessing_result.outputs["emotion"], "gender":state_2_preprocessing_result.outputs["gender"]}
        state_2_processing_result = await sm.executePipelineState(state_2_processing_input)
        print("state_2_processing_result:", state_2_processing_result)

        # go to the tts state
        await sm.transitionPipeline("tts")
        state_2_tts_input = {"text":state_2_processing_result.outputs["llm_answer"], "emotion":state_2_preprocessing_result.outputs["emotion"], "gender":state_2_preprocessing_result.outputs["gender"]}
        state_2_tts_result = await sm.executePipelineState(state_2_tts_input)
        print("state_2_tts_result:", state_2_tts_result)

        # Step 3: Simulate state 3
        await sm.transitionWorkflow("state_3")
        state_3_stt_input = {"audio_path": "fillerWords\لحضة واحدة مع حضرتك.mp3"}
        state_3_stt_result = await sm.executePipelineState(state_3_stt_input)
        print("state_3_stt_result:", state_3_stt_result)

        # go to the preprocessing state
        await sm.transitionPipeline("preprocessing")
        state_3_preprocessing_input = {"transcript":state_3_stt_result.outputs["text"]}
        state_3_preprocessing_result = await sm.executePipelineState(state_3_preprocessing_input)
        print("state_3_preprocessing_result:", state_3_preprocessing_result)

        # go to the filler tts state
        await sm.transitionPipeline("filler_tts")
        state_3_filler_tts_input = {"filler_text":"I am here with you, just a second"}
        state_3_filler_tts_result = await sm.executePipelineState(state_3_filler_tts_input)
        print("state_3_filler_tts_result:", state_3_filler_tts_result)

        # go to the processing state
        await sm.transitionPipeline("processing")
        state_3_processing_input = {"clean_text":state_3_preprocessing_result.outputs["clean_text"], "intent":state_3_preprocessing_result.outputs["intent"], "emotion":state_3_preprocessing_result.outputs["emotion"], "gender":state_3_preprocessing_result.outputs["gender"]}
        state_3_processing_result = await sm.executePipelineState(state_3_processing_input)
        print("state_3_processing_result:", state_3_processing_result)

        # go to the tts state
        await sm.transitionPipeline("tts")
        state_3_tts_input = {"text":state_3_processing_result.outputs["llm_answer"], "emotion":state_3_preprocessing_result.outputs["emotion"], "gender":state_3_preprocessing_result.outputs["gender"]}
        state_3_tts_result = await sm.executePipelineState(state_3_tts_input)
        print("state_3_tts_result:", state_3_tts_result)

    except Exception as e:
        logger.error(f"Error during trial setup: {e}")
        return StateOutput(
            status=StatusType.ERROR,
            message=str(e),
            code=StatusCode.INTERNAL_ERROR,
            outputs={},
            meta={"source": "test_suite"}
        )
    
if __name__ == "__main__":
    try:
        setup_development_logging()
        asyncio.run(run_trial())
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
    finally:
        cleanup_logger()  # Ensure logger is cleaned up properly

