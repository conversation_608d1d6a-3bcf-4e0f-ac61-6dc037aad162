import asyncio
import os
import sys

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

from core.session_manager_v2 import Session<PERSON>anagerV2

async def main():
    # Initialize SessionManagerV2
    session_manager = SessionManagerV2()
    workflow_name = 'banking_workflow.json'
    user_id = 'demo_user'

    # Create a new session
    session_id = await session_manager.create_session(workflow_name, user_id)
    print(f"Session created: {session_id}")

    # Retrieve the memory manager for the session
    memory_manager = session_manager.active_sessions[session_id]["memory_manager"]
    # Set the audio file path in contextual memory before running the orchestrator
    # NOTE: The key 'audio_path' must match the pipeline config mapping for the 'stt' step input in l2_greeting_banking_system.json
    await memory_manager.set("contextual", "audio_path", "fillerWords/user_conversation_part_1.mp3")
    print("Audio file path set in contextual memory.")

    # Retrieve the orchestrator (OrchestratorV3) for the session
    orchestrator = await session_manager.initialize_orchestrator(session_id)

    # Run the orchestrator
    print("Running OrchestratorV3...")
    summary = await orchestrator.run()
    print("OrchestratorV3 run summary:")
    print(summary)

    # Clean up the session
    await session_manager.cleanup_session(session_id, reason="demo_complete")
    print("Session cleaned up.")

if __name__ == "__main__":
    asyncio.run(main()) 