{"workflow": {"id": "banking_customer_service", "name": "Banking Customer Service Flow", "version": "2.0", "start": "state_greeting", "allowed_actions": ["Verify account", "Check account balance", "Request transaction history", "Inquire about account services"], "prohibited_actions": ["Do not share sensitive account details", "Do not process unauthorized transactions"], "states": {"state_greeting": {"id": "state_greeting", "type": "input", "layer2_id": "l2_greeting_banking_system", "expected_input": [], "expected_output": ["audio_path", "latencyTTS", "intent"], "transitions": [{"condition": "intent == 'verify_account'", "target": "state_verify_account"}, {"condition": "intent == 'check_balance'", "target": "state_balance_inquiry"}, {"condition": "intent == 'transaction_history'", "target": "state_transaction_history"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"]}, "state_verify_account": {"id": "state_verify_account", "type": "input", "layer2_id": "l2_verify_account", "expected_input": ["customer_id", "security_answer"], "expected_output": ["verification_status", "audio_path", "latencyTTS"], "transitions": [{"condition": "verification_status == 'success'", "target": "state_greeting"}, {"condition": "verification_status == 'failed'", "target": "state_goodbye"}], "allowed_tools": ["STT", "LLM", "TTS", "DB"]}, "state_balance_inquiry": {"id": "state_balance_inquiry", "type": "inform", "layer2_id": "l2_balance_inquiry", "expected_input": ["account_id"], "expected_output": ["account_balance", "audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "state_goodbye"}], "allowed_tools": ["LLM", "TTS", "DB"]}, "state_transaction_history": {"id": "state_transaction_history", "type": "inform", "layer2_id": "l2_balance_inquiry", "expected_input": ["account_id", "date_range"], "expected_output": ["transaction_list", "audio_path", "latencyTTS"], "transitions": [{"condition": "true", "target": "state_goodbye"}], "allowed_tools": ["LLM", "TTS", "DB"]}, "state_goodbye": {"id": "state_goodbye", "type": "end", "layer2_id": "l2_greeting_banking_system", "expected_input": [], "expected_output": ["audio_path", "latencyTTS", "exit_signal"], "transitions": [], "allowed_tools": ["TTS"]}}}}