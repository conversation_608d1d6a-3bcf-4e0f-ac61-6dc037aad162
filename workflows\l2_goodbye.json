{"id": "l2_goodbye", "version": "1.0", "pipeline": [{"step": "LLM_Goodbye", "agent": "llm_farewell_gen", "input": {}, "tools": {"external_tools": "openai"}, "output": {"text": "farewell_text"}}, {"step": "TTS_Farewell", "agent": "tts_google", "input": {"text": "farewell_text"}, "tools": {"external_tools": "openai"}, "output": {"audio": "audio_output", "exit_signal": "exit_signal"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "LLM_Goodbye"}, "onError": {"retry": 1, "fallback_state": "l2_fallback_generic"}}