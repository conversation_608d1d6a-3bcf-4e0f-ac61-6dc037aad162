{"id": "l2_loan_denied", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"feedback_request": "feedback_request", "latencyPreprocessing": "latencyPreprocessing", "fallback_message": "fallback_message"}}, {"step": "filler_tts", "process": "filler_tts_process", "input": {"fallback_message": "fallback_message"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyFillerTTS": "latencyFillerTTS"}}, {"step": "processing", "process": "processing_process", "input": {"feedback_request": "feedback_request"}, "tools": {"external_tools": "compliance_check"}, "output": {"denial_reason": "denial_reason", "improvement_suggestions": "improvement_suggestions", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "input": {"text": "denial_reason"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_denied"}, "outputs": ["apology_message"]}