{"id": "l2_greeting", "version": "1.0", "pipeline": [{"step": "stt", "process": "stt_process", "input": {"audio": "audio_path"}, "tools": {"external_tools": "stt"}, "output": {"text": "text", "latencySTT": "latencySTT", "audio_path": "audio_path"}}, {"step": "preprocessing", "process": "preprocessing_process", "input": {"text": "text"}, "tools": {"external_tools": "openai"}, "output": {"fallback_message": "fallback_message", "emotion": "emotion", "intent": "intent", "latencyPreprocessing": "latencyPreprocessing", "clean_text": "clean_text"}}, {"step": "filler_tts", "process": "filler_tts_process", "input": {"fallback_message": "fallback_message"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyFillerTTS": "latencyFillerTTS"}}, {"step": "processing", "process": "processing_process", "input": {"text": "clean_text", "intent": "intent", "emotion": "emotion"}, "tools": {"external_tools": "openai"}, "output": {"greeting_response": "greeting_response", "latencyProcessing": "latencyProcessing"}}, {"step": "tts", "process": "tts_process", "input": {"text": "greeting_response"}, "tools": {"external_tools": "openai"}, "output": {"audio_path": "audio_path", "latencyTTS": "latencyTTS"}}], "onInterrupt": {"handler": "interrupt_manager", "resume_from": "stt"}, "onError": {"retry": 2, "fallback_state": "l2_fallback_generic"}}